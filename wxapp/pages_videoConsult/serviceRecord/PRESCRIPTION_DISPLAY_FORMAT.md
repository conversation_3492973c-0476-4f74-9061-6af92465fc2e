# 处方详细信息显示格式优化

## 功能概述

根据后端返回的数据格式，优化了处方详细信息的显示，特别是用药清单的展示方式，使其更符合实际的数据结构和用户体验需求。

## 后端数据格式

根据提供的后端数据格式：

```json
{
  "recomId": 17,
  "doctorId": 1,
  "doctorName": "陈结吉",
  "authStatus": null,
  "recomTime": "2025-06-21 17:55:56",
  "consultType": null,
  "diagnosis": "流行性感冒 J11.101",
  "drugType": 1,
  "expire": 0,
  "inquirerId": 9,
  "items": [
    {
      "pid": 1,
      "skuId": 1,
      "name": "感冒灵 1g/盒",
      "commonName": "感冒灵",
      "quantity": 1,
      "usages": "每日1次,每次1g,饭前口服,1天",
      "backup": "",
      "point": null
    }
  ],
  "patientId": 2,
  "patientName": "张中桥",
  "sessionId": null,
  "status": 0,
  "videoConsultId": null
}
```

## 显示格式优化

### 1. WXML模板优化

```xml
<!-- 详细信息 -->
<view class="p20">
  <view class="f28 c333">就诊人：{{item.patientName || '-'}} {{item.patientGender == 1 ?'男':'女'}} {{item.patientAge || ''}}</view>
  <view class="f28 c333 mt10">开药医生：{{item.doctorName || '-'}}</view>
  <view class="f28 c333 mt10">开药时间：{{item.recomTime || '-'}}</view>
  <view class="f28 c333 mt10" wx:if="{{item.items && item.items.length > 0}}">
    <view class="c333 f28 mb10">用药清单：</view>
    <view wx:for="{{item.items}}" wx:key="pid" wx:for-item="medicine" wx:for-index="medIndex">
      <view class="medicine-item">
        <view class="medicine-name">{{medicine.name || medicine.commonName || '-'}}</view>
        <view class="medicine-usage" wx:if="{{medicine.usages}}">用法用量：{{medicine.usages}}</view>
        <view class="medicine-quantity" wx:if="{{medicine.quantity}}">数量：{{medicine.quantity}}</view>
      </view>
    </view>
    <view class="c999 f22 mt10">共{{item.items.length}}种药品</view>
  </view>
</view>
```

### 2. 字段映射

根据后端数据结构，优化了字段映射：

| 显示内容 | 后端字段 | 备用字段 |
|---------|---------|---------|
| 就诊人姓名 | `patientName` | - |
| 就诊人性别 | `patientGender` | - |
| 就诊人年龄 | `patientAge` | - |
| 开药医生 | `doctorName` | - |
| 开药时间 | `recomTime` | - |
| 药品名称 | `medicine.name` | `medicine.commonName` |
| 用法用量 | `medicine.usages` | - |
| 药品数量 | `medicine.quantity` | - |

### 3. 样式优化

为药品项添加了专门的样式：

```css
/* 药品项样式 */
.medicine-item {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #367DFF;
  margin-bottom: 15rpx;
  padding: 15rpx 20rpx;
  transition: all 0.3s ease;
}

.medicine-item:last-child {
  margin-bottom: 0;
}

.medicine-item:hover {
  background-color: #f0f2f5;
}

.medicine-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.medicine-usage,
.medicine-quantity {
  font-size: 24rpx;
  color: #666;
  margin-top: 5rpx;
  line-height: 1.4;
}
```

## 显示效果

### 1. 药品清单展示

- **药品名称**：优先显示 `name` 字段，备用 `commonName`
- **用法用量**：显示详细的用药说明
- **药品数量**：显示具体数量
- **视觉效果**：每个药品项有独立的卡片样式，左侧蓝色边框突出显示

### 2. 基本信息展示

- **就诊人信息**：姓名、性别、年龄
- **医生信息**：开药医生姓名
- **时间信息**：开药时间

### 3. 统计信息

- **药品总数**：显示"共X种药品"

## 技术特点

### 1. 数据结构适配

- **数组遍历**：正确遍历 `items` 数组
- **字段映射**：支持多种字段名映射
- **空值处理**：对空值进行友好处理

### 2. 用户体验优化

- **视觉层次**：清晰的信息层次结构
- **样式美观**：现代化的卡片式设计
- **信息完整**：显示所有必要的药品信息

### 3. 响应式设计

- **适配性**：适配不同屏幕尺寸
- **交互性**：支持点击和滑动交互
- **性能优化**：高效的渲染性能

## 数据处理逻辑

### 1. 条件渲染

```xml
<!-- 只有当items数组存在且不为空时才显示用药清单 -->
<view wx:if="{{item.items && item.items.length > 0}}">
  <!-- 用药清单内容 -->
</view>
```

### 2. 循环渲染

```xml
<!-- 遍历items数组，为每个药品创建独立的显示项 -->
<view wx:for="{{item.items}}" wx:key="pid" wx:for-item="medicine" wx:for-index="medIndex">
  <!-- 药品详细信息 -->
</view>
```

### 3. 字段优先级

```xml
<!-- 药品名称：优先使用name，备用commonName -->
{{medicine.name || medicine.commonName || '-'}}
```

## 兼容性考虑

### 1. 字段兼容

- 支持 `name` 和 `commonName` 两种药品名称字段
- 对缺失字段显示默认值 `-`

### 2. 数据格式兼容

- 兼容空数组情况
- 兼容字段缺失情况
- 兼容数据类型变化

### 3. 版本兼容

- 保持向后兼容性
- 支持渐进式升级

## 测试建议

### 1. 数据测试

- 测试完整数据的显示
- 测试部分字段缺失的情况
- 测试空数组的处理
- 测试单个药品和多个药品的显示

### 2. 样式测试

- 测试不同屏幕尺寸的显示效果
- 测试长文本的处理
- 测试药品项的间距和对齐

### 3. 交互测试

- 测试点击处方项的跳转
- 测试滚动性能
- 测试加载状态

## 注意事项

### 1. 数据结构

确保后端返回的数据结构与前端处理逻辑匹配：

- `items` 字段必须是数组
- 每个药品项必须包含 `pid` 作为唯一标识
- 药品名称至少包含 `name` 或 `commonName` 之一

### 2. 性能考虑

- 对于大量药品的处理，考虑虚拟滚动
- 优化渲染性能，避免不必要的重渲染

### 3. 用户体验

- 确保信息显示完整且易读
- 保持一致的视觉风格
- 提供清晰的操作反馈

## 总结

通过这次优化，处方详细信息的显示更加：

1. ✅ **准确**：完全匹配后端数据结构
2. ✅ **美观**：现代化的卡片式设计
3. ✅ **完整**：显示所有必要的药品信息
4. ✅ **友好**：良好的用户体验
5. ✅ **兼容**：支持多种数据格式

这些改进确保了处方信息能够准确、美观地展示给用户，提升了整体的用户体验。
