# 视频咨询页面集成病情记录和免责声明功能

## 修改概述

参考 `pages/diseaseDetail/diseaseDetail` 页面，将 `diseaseAdd` 方法和 `disclaimer` 方法集成到视频咨询页面中。修改用户同意协议后的流程，不再直接开始呼叫，而是先调用这两个方法，然后再开始呼叫医生。

## 主要修改内容

### 1. 数据结构扩展

#### 新增病情相关数据字段

```javascript
// 病情相关数据
recordId: null, //病情记录ID
diseaseInfo: {
  offlineSeeDoctor: 1, //1是  0否
  offlineDiagnosis: '无', //线下诊断，默认为"无"
  dataLossTag: 0, // 资料丢失0否 1是
  offlineDiagnosisImgs: [], //图片
  inquirerId: '', //就诊人ID
  doctorId: '', //医生ID
  description: '无', //病情描述，默认为"无"
  symptom: {
    fever: null, //是否发热
    pregnant: null, //是否为孕妇
    hypertension: null, //是否患有高血压
    diabetes: null //是否患有糖尿病
  }
}
```

### 2. 流程修改

#### 修改前的 `onAgreement` 方法：
```javascript
onAgreement() {
  this.setData({
    checked: true,
    isAgreement: true,
    showPopup: false
  })

  // 同意协议后，开始呼叫医生
  this.startVideoCall()
}
```

#### 修改后的 `onAgreement` 方法：
```javascript
async onAgreement() {
  this.setData({
    checked: true,
    isAgreement: true,
    showPopup: false
  })

  // 同意协议后，先调用 diseaseAdd 和 disclaimer，然后开始呼叫医生
  try {
    wx.showLoading({
      title: '处理中...',
      mask: true
    })

    // 准备病情数据
    this.prepareDiseaseInfo()

    // Step 1: 添加病情记录
    const diseaseAddResult = await this.diseaseAdd()
    if (!diseaseAddResult) {
      wx.hideLoading()
      return
    }

    // Step 2: 调用免责声明接口
    const disclaimerResult = await this.disclaimer()
    if (disclaimerResult === undefined || disclaimerResult === null) {
      wx.hideLoading()
      return
    }

    wx.hideLoading()

    // Step 3: 开始呼叫医生
    this.startVideoCall()

  } catch (err) {
    console.error('同意协议后处理失败:', err)
    wx.hideLoading()
    wx.showToast({
      title: '处理失败，请重试',
      icon: 'none'
    })
  }
}
```

### 3. 新增方法

#### 3.1 `prepareDiseaseInfo()` - 准备病情数据
```javascript
prepareDiseaseInfo() {
  const currentPatient = this.data.currentPatient
  if (currentPatient) {
    this.setData({
      'diseaseInfo.inquirerId': currentPatient.inquirerId,
      'diseaseInfo.doctorId': this.data.doctorId
    })
  }
  console.log('准备病情数据:', this.data.diseaseInfo)
}
```

#### 3.2 `diseaseAdd()` - 添加病情记录
```javascript
async diseaseAdd() {
  console.log('开始添加病情记录')
  
  try {
    const diseaseInfo = { ...this.data.diseaseInfo }
    
    console.log('调用 diseaseAdd 接口，参数:', diseaseInfo)

    const res = await util.request(api.diseaseAdd, diseaseInfo, 'post', 1)
    
    if (res.data.code === 0) {
      console.log('病情记录添加成功，recordId:', res.data.data)
      this.setData({
        recordId: res.data.data
      })
      return true
    } else {
      console.error('病情记录添加失败:', res.data.msg)
      wx.showToast({
        title: res.data.msg || '添加病情记录失败',
        icon: 'none'
      })
      return false
    }
  } catch (error) {
    console.error('病情记录添加异常:', error)
    wx.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
    return false
  }
}
```

#### 3.3 `disclaimer()` - 免责声明接口
```javascript
async disclaimer() {
  console.log('开始调用免责声明接口')
  
  try {
    const params = {
      doctorId: this.data.doctorId,
      inquirerId: this.data.diseaseInfo.inquirerId,
      type: 2, // 视频咨询
      agree: true,
      recordId: this.data.recordId,
      consultType: 2, // 视频咨询类型
      source: null // 分享来源，视频咨询页面暂时为空
    }

    console.log('调用 disclaimer 接口，参数:', params)

    const res = await util.request(api.disclaimer, params, 'post', 2)
    
    if (res.data.code === 0) {
      console.log('免责声明接口调用成功，返回数据:', res.data.data)
      return res.data.data
    } else if (res.data.code === 210) {
      // 特殊错误码处理
      wx.showModal({
        content: res.data.msg,
        showCancel: false,
        confirmText: '确定'
      })
      return null
    } else {
      console.error('免责声明接口调用失败:', res.data.msg)
      wx.showToast({
        title: res.data.msg || '免责声明处理失败',
        icon: 'none'
      })
      return null
    }
  } catch (error) {
    console.error('免责声明接口调用异常:', error)
    wx.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
    return null
  }
}
```

## 流程图

### 修改前的流程：
```
用户同意协议 → 直接开始呼叫医生
```

### 修改后的流程：
```
用户同意协议 
    ↓
准备病情数据 (prepareDiseaseInfo)
    ↓
添加病情记录 (diseaseAdd)
    ↓
调用免责声明 (disclaimer)
    ↓
开始呼叫医生 (startVideoCall)
```

## 接口调用详情

### 1. diseaseAdd 接口

#### 请求参数：
```javascript
{
  offlineSeeDoctor: 1,        // 1是  0否
  offlineDiagnosis: '无',     // 线下诊断
  dataLossTag: 0,             // 资料丢失0否 1是
  offlineDiagnosisImgs: [],   // 图片数组
  inquirerId: 'xxx',          // 就诊人ID
  doctorId: 'xxx',            // 医生ID
  description: '无',          // 病情描述
  symptom: {                  // 症状信息
    fever: null,              // 是否发热
    pregnant: null,           // 是否为孕妇
    hypertension: null,       // 是否患有高血压
    diabetes: null            // 是否患有糖尿病
  }
}
```

#### 响应处理：
- **成功 (code=0)**: 保存返回的 `recordId`
- **失败**: 显示错误信息，中断流程

### 2. disclaimer 接口

#### 请求参数：
```javascript
{
  doctorId: 'xxx',           // 医生ID
  inquirerId: 'xxx',         // 就诊人ID
  type: 2,                   // 咨询类型：2=视频咨询
  agree: true,               // 是否同意
  recordId: 'xxx',           // 病情记录ID
  consultType: 2,            // 咨询类型：2=视频咨询
  source: null               // 分享来源
}
```

#### 响应处理：
- **成功 (code=0)**: 继续执行后续流程
- **特殊错误 (code=210)**: 显示模态框，中断流程
- **其他错误**: 显示错误信息，中断流程

## 错误处理

### 1. 网络错误
- 显示 "网络错误，请重试" 提示
- 中断流程，用户可重新尝试

### 2. 业务错误
- 显示后端返回的具体错误信息
- 中断流程，用户可根据提示处理

### 3. 特殊错误码 210
- 显示模态框提示用户
- 中断流程

## 数据流转

### 1. 病情数据准备
```
currentPatient.inquirerId → diseaseInfo.inquirerId
doctorId → diseaseInfo.doctorId
```

### 2. 病情记录创建
```
diseaseInfo → api.diseaseAdd → recordId
```

### 3. 免责声明确认
```
{doctorId, inquirerId, recordId, ...} → api.disclaimer → 继续流程
```

## 与原 diseaseDetail 页面的差异

### 1. 数据来源
- **diseaseDetail**: 用户手动填写病情信息
- **视频咨询**: 使用默认值（"无"）

### 2. 症状收集
- **diseaseDetail**: 根据配置决定是否收集症状
- **视频咨询**: 保留症状字段，但使用默认值

### 3. 后续流程
- **diseaseDetail**: 根据 disclaimer 结果决定支付或免费流程
- **视频咨询**: 直接进入呼叫医生流程

## 用户体验改进

### 1. 加载提示
- 显示 "处理中..." 加载提示
- 避免用户重复操作

### 2. 错误反馈
- 明确的错误信息提示
- 友好的用户引导

### 3. 流程透明
- 详细的控制台日志
- 便于问题排查和调试

## 测试要点

### 1. 正常流程测试
- 用户同意协议
- 病情记录成功创建
- 免责声明成功确认
- 成功进入呼叫医生流程

### 2. 异常情况测试
- 网络异常时的处理
- 接口返回错误时的处理
- 特殊错误码 210 的处理

### 3. 数据验证测试
- 就诊人信息正确传递
- 医生信息正确传递
- recordId 正确保存和使用

## 注意事项

### 1. 数据一致性
- 确保 inquirerId 和 doctorId 正确传递
- 确保 recordId 在后续流程中正确使用

### 2. 错误处理
- 任何步骤失败都应中断流程
- 提供明确的错误信息给用户

### 3. 性能考虑
- 接口调用是串行的，确保数据依赖关系
- 适当的加载提示改善用户体验

### 4. 兼容性
- 保持与原有流程的兼容性
- 不影响其他功能的正常使用
