# 视频问诊状态管理优化

## 功能概述

优化了视频问诊的状态管理，通过 `videoConsultId` 来区分是否已经发起过问诊，避免重复发起问诊，提升用户体验。

## 问题背景

### 原有问题

1. **重复发起问诊**：用户呼叫医生后挂断返回上一页，再次点击呼叫医生时会重复发起问诊
2. **状态不明确**：无法区分是首次发起问诊还是再次呼叫已存在的问诊
3. **接口调用混乱**：不同状态下应该调用不同的接口

### 业务场景

```
用户首次呼叫医生
→ 调用 videoPayInfo 接口发起问诊
→ 获得 videoConsultId
→ 跳转到呼叫页面
→ 用户挂断返回上一页
→ 用户再次点击呼叫医生
→ 应该调用 startVideoCall 接口（而不是重新发起问诊）
```

## 解决方案

### 核心思路

通过 `videoConsultId` 的存在与否来判断问诊状态：

1. **无 videoConsultId**：首次发起问诊，调用 `videoPayInfo` 接口
2. **有 videoConsultId**：已发起过问诊，调用 `startVideoCall` 接口

### 实现逻辑

#### 1. 页面初始化时获取 videoConsultId

```javascript
// 在 initPageWithUserInfo 方法中
this.setData({
  consultId: options.consultId || options.videoConsultId || options.id,
  videoConsultId: options.videoConsultId || null, // 视频咨询ID，用于判断是否已发起过问诊
  // ... 其他参数
})
```

#### 2. 呼叫医生时的状态判断

```javascript
async startVideoCall() {
  try {
    wx.showLoading({
      title: this.data.videoConsultId ? '发起视频通话...' : '发起视频咨询...',
      mask: true
    })

    // 检查是否已经发起过问诊
    if (this.data.videoConsultId) {
      console.log('已发起过问诊，直接调用 startVideoCall 接口')
      // 已发起过问诊，直接调用 startVideoCall 接口
      const callResult = await this.callExistingVideoConsult()
      // 处理结果...
    } else {
      console.log('首次发起问诊，调用 videoPayInfo 接口')
      // 首次发起问诊，调用 videoPayInfo API 发起视频咨询
      const videoPayResult = await this.initiateVideoConsultation()
      
      if (videoPayResult.success) {
        // 保存 videoConsultId，标记已发起过问诊
        this.setData({
          videoConsultId: videoPayResult.data.videoConsultId
        })
        // 处理结果...
      }
    }
  } catch (err) {
    // 错误处理...
  }
}
```

#### 3. 新增 callExistingVideoConsult 方法

```javascript
async callExistingVideoConsult() {
  try {
    const currentPatient = this.data.currentPatient
    if (!currentPatient) {
      return {
        success: false,
        message: '请先选择就诊人'
      }
    }

    // 构建 startVideoCall API 参数
    const params = {
      patientId: currentPatient.inquirerId, // 使用 inquirerId 作为 patientId
      videoConsultId: this.data.videoConsultId // 已存在的视频咨询ID
    }

    console.log('调用 startVideoCall API，参数:', params)

    const res = await util.request(api.startVideoCall, params, 'post', '2')

    if (res.data.code === 0) {
      // 处理成功响应，提取医生信息等
      const responseData = res.data.data
      const doctorInfo = {
        avatar: responseData.doctorHeadUrl || responseData.doctorPhoto || responseData.headUrl || '',
        name: responseData.doctorName || responseData.name || '医生',
        title: responseData.doctorTitle || responseData.title || '',
        specialty: responseData.doctorExpertise || responseData.expertise || responseData.specialty || '',
        goodRate: responseData.goodRate || '',
        serviceCount: responseData.serviceCount || 0
      }

      return {
        success: true,
        data: {
          videoConsultId: this.data.videoConsultId,
          doctorInfo: doctorInfo,
          roomId: responseData.roomId,
          originalData: responseData
        }
      }
    } else {
      return {
        success: false,
        message: res.data.msg || '发起视频通话失败'
      }
    }
  } catch (err) {
    return {
      success: false,
      message: '网络异常，请稍后重试'
    }
  }
}
```

## 状态流转图

```
页面初始化
↓
检查 URL 参数中的 videoConsultId
├─ 有 videoConsultId → 标记为已发起状态
└─ 无 videoConsultId → 标记为未发起状态
↓
用户点击呼叫医生
├─ 未发起状态 → 调用 videoPayInfo 接口
│   ├─ 成功 → 保存 videoConsultId，跳转呼叫页面
│   └─ 失败 → 显示错误信息
└─ 已发起状态 → 调用 startVideoCall 接口
    ├─ 成功 → 跳转呼叫页面
    └─ 失败 → 显示错误信息
```

## 接口调用对比

### 首次发起问诊（videoPayInfo）

**接口**：`api.videoPayInfo`
**参数**：
```javascript
{
  inquirerId: currentPatient.inquirerId,
  price: 0,
  conditionDesc: '无',
  offlineDiagnosis: '无',
  subOrderCode: this.data.subOrderCode,
  packageCode: this.data.packageCode,
  assistantWelcomeId: this.data.assistantWelcomeId // 可选
}
```

**返回**：
```javascript
{
  code: 0,
  data: {
    videoConsultId: "12345", // 新生成的视频咨询ID
    doctorName: "张医生",
    doctorHeadUrl: "...",
    roomId: "room123",
    // ... 其他医生信息
  }
}
```

### 再次呼叫（startVideoCall）

**接口**：`api.startVideoCall`
**参数**：
```javascript
{
  patientId: currentPatient.inquirerId,
  videoConsultId: this.data.videoConsultId // 已存在的视频咨询ID
}
```

**返回**：
```javascript
{
  code: 0,
  data: {
    doctorName: "张医生",
    doctorHeadUrl: "...",
    roomId: "room123",
    // ... 其他医生信息
  }
}
```

## 用户体验优化

### 1. Loading 文案区分

```javascript
wx.showLoading({
  title: this.data.videoConsultId ? '发起视频通话...' : '发起视频咨询...',
  mask: true
})
```

- **首次发起**：显示"发起视频咨询..."
- **再次呼叫**：显示"发起视频通话..."

### 2. 日志信息优化

```javascript
console.log('视频咨询参数:', {
  consultId: this.data.consultId,
  videoConsultId: this.data.videoConsultId,
  hasVideoConsultId: !!this.data.videoConsultId,
  // ... 其他参数
})
```

### 3. 状态判断日志

```javascript
if (this.data.videoConsultId) {
  console.log('已发起过问诊，直接调用 startVideoCall 接口')
} else {
  console.log('首次发起问诊，调用 videoPayInfo 接口')
}
```

## 测试场景

### 1. 首次发起问诊

**步骤**：
1. 用户进入视频咨询室页面（无 videoConsultId 参数）
2. 选择就诊人，勾选协议
3. 点击呼叫医生按钮

**预期结果**：
- 显示"发起视频咨询..."
- 调用 `videoPayInfo` 接口
- 成功后保存 `videoConsultId`
- 跳转到呼叫医生页面

### 2. 再次呼叫已发起的问诊

**步骤**：
1. 用户从呼叫页面返回（已有 videoConsultId）
2. 再次点击呼叫医生按钮

**预期结果**：
- 显示"发起视频通话..."
- 调用 `startVideoCall` 接口
- 跳转到呼叫医生页面

### 3. 从历史列表进入（待接诊状态）

**步骤**：
1. 用户从服务记录页面进入（带有 videoConsultId 参数）
2. 点击呼叫医生按钮

**预期结果**：
- 识别为已发起状态
- 调用 `startVideoCall` 接口

## 数据流向

### 1. 页面参数传递

```
服务记录页面 → 视频咨询室页面
URL: /pages_videoConsult/consultRoom/index?videoConsultId=12345&doctorId=123&...

呼叫医生页面 → 视频咨询室页面（返回）
保持原有的 videoConsultId 状态
```

### 2. 状态保存

```javascript
// 首次发起成功后保存状态
this.setData({
  videoConsultId: videoPayResult.data.videoConsultId
})
```

### 3. 全局数据传递

```javascript
// 医生信息存储到全局数据
const app = getApp()
if (app.globalData) {
  app.globalData.videoCallDoctorInfo = doctorInfo
}
```

## 错误处理

### 1. 参数验证

```javascript
// 验证必要参数
if (!params.patientId) {
  return {
    success: false,
    message: '用户信息异常，请重新登录'
  }
}

if (!params.videoConsultId) {
  return {
    success: false,
    message: '问诊信息异常，请重试'
  }
}
```

### 2. 接口调用失败

```javascript
if (res.data.code === 0) {
  // 成功处理
} else {
  return {
    success: false,
    message: res.data.msg || '发起视频通话失败'
  }
}
```

### 3. 网络异常

```javascript
try {
  // 接口调用
} catch (err) {
  console.error('接口调用异常', err)
  return {
    success: false,
    message: '网络异常，请稍后重试'
  }
}
```

## 相关文件

- **主要文件**：`wxapp/pages_videoConsult/consultRoom/index.js`
- **修改方法**：
  - `initPageWithUserInfo()` - 初始化时获取 videoConsultId
  - `startVideoCall()` - 主要的呼叫逻辑
  - `callExistingVideoConsult()` - 新增的再次呼叫方法
  - `initiateVideoConsultation()` - 原有的首次发起方法

## 总结

通过这次优化：

1. ✅ **状态明确**：通过 videoConsultId 明确区分问诊状态
2. ✅ **避免重复**：已发起的问诊不会重复发起
3. ✅ **接口正确**：不同状态调用正确的接口
4. ✅ **用户体验**：Loading 文案和流程更清晰
5. ✅ **调试友好**：详细的日志信息便于问题排查

现在用户在呼叫医生后挂断返回，再次点击呼叫医生时，系统会正确识别为已发起状态，直接调用 `startVideoCall` 接口，而不会重复发起问诊。
