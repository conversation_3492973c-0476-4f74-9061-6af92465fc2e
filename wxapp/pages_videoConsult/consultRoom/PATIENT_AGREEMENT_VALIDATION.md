# 就诊人切换与知情同意书校验优化

## 功能概述

优化了就诊人切换和知情同意书勾选的逻辑，确保切换就诊人时重置协议状态，并在勾选知情同意书时校验就诊人的实名认证状态。

## 需求背景

### 原有问题

1. **状态不重置**：切换就诊人时，知情同意书的勾选状态没有重置，可能导致状态混乱
2. **缺少实名校验**：点击知情同意书时没有校验当前就诊人是否实名，可能导致未实名用户也能勾选协议

### 业务需求

1. **切换就诊人时重置状态**：每次切换就诊人都应该重置知情同意书的勾选状态
2. **实名校验**：只有完成实名认证（有身份证号码）的就诊人才能勾选知情同意书

## 解决方案

### 1. 切换就诊人时重置协议状态

#### 修改 selectPatient 方法

```javascript
selectPatient(e) {
  const index = e.currentTarget.dataset.index
  const selectedPatient = this.data.patientList[index] || null
  
  this.setData({
    currentPatientIndex: index,
    currentPatient: selectedPatient,
    // 切换就诊人时重置知情同意书的勾选状态
    checked: false,
    isAgreement: false
  })

  console.log('选择就诊人:', {
    index: index,
    patient: selectedPatient,
    resetAgreementStatus: '已重置知情同意书勾选状态'
  })

  // 自动滑动到选中的就诊人位置
  this.scrollToPatient(index)
}
```

**重置的状态**：
- `checked: false` - 取消知情同意书勾选
- `isAgreement: false` - 重置协议同意状态

### 2. 知情同意书勾选时的实名校验

#### 修改 onChecked 方法

```javascript
onChecked(e) {
  const checked = e.detail

  if (checked) {
    // 勾选时先校验当前就诊人是否实名
    if (!this.validatePatientRealName()) {
      // 实名校验失败，不允许勾选
      return
    }

    // 实名校验通过，设置勾选状态
    this.setData({
      checked: checked
    })

    // 如果还未同意过协议，则显示协议弹框
    if (!this.data.isAgreement) {
      this.showAgreementPopup()
    }
  } else {
    // 如果是取消勾选，重置协议同意状态
    this.setData({
      checked: checked,
      isAgreement: false
    })
  }
}
```

**校验流程**：
1. 用户点击勾选知情同意书
2. 调用 `validatePatientRealName()` 校验实名状态
3. 校验通过才允许勾选，否则显示提示弹框

### 3. 实名认证校验逻辑

#### 新增 validatePatientRealName 方法

```javascript
validatePatientRealName() {
  const currentPatient = this.data.currentPatient

  // 检查是否选择了就诊人
  if (!currentPatient) {
    wx.showToast({
      title: '请先选择就诊人',
      icon: 'none',
      duration: 3000
    })
    return false
  }

  // 检查就诊人是否有身份证号码（实名认证的标志）
  const hasIdCard = !!(currentPatient.idCard && currentPatient.idCard.trim())
  
  console.log('校验就诊人实名状态:', {
    patientName: currentPatient.name,
    hasIdCard: hasIdCard,
    idCard: currentPatient.idCard ? '已填写' : '未填写'
  })

  if (!hasIdCard) {
    wx.showModal({
      title: '实名认证提醒',
      content: `当前就诊人"${currentPatient.name}"尚未完成实名认证，请先完善身份证信息后再勾选知情同意书。`,
      showCancel: true,
      cancelText: '稍后处理',
      confirmText: '去完善',
      success: (res) => {
        if (res.confirm) {
          // 跳转到就诊人编辑页面
          this.editCurrentPatient()
        }
      }
    })
    return false
  }

  return true
}
```

**校验标准**：
- 以 `currentPatient.idCard` 字段是否有值作为实名认证的判断标准
- 身份证号码不为空且去除空格后有内容，则认为已实名

### 4. 编辑当前就诊人功能

#### 新增 editCurrentPatient 方法

```javascript
editCurrentPatient() {
  const currentPatient = this.data.currentPatient
  
  if (!currentPatient) {
    wx.showToast({
      title: '请先选择就诊人',
      icon: 'none',
      duration: 3000
    })
    return
  }

  // 构建编辑页面URL
  let editUrl = ''
  
  if (currentPatient.inquirerId) {
    // 有 inquirerId，跳转到编辑页面
    editUrl = `/pages/peopleContent/detail/detail?inquirerId=${currentPatient.inquirerId}`
    
    // 添加视频咨询相关参数，用于编辑完成后的页面刷新
    if (this.data.subOrderCode) {
      editUrl += `&subOrderCode=${this.data.subOrderCode}`
    }
    if (this.data.packageCode) {
      editUrl += `&packageCode=${this.data.packageCode}`
    }
    if (currentPatient.holderId) {
      editUrl += `&holderId=${currentPatient.holderId}`
    }
  } else {
    // 没有 inquirerId，跳转到新增页面
    editUrl = '/pages/peopleContent/addPeople/addPeople'
    
    // 添加视频咨询相关参数
    if (this.data.subOrderCode) {
      editUrl += `?subOrderCode=${this.data.subOrderCode}`
    }
    if (this.data.packageCode) {
      editUrl += `${editUrl.includes('?') ? '&' : '?'}packageCode=${this.data.packageCode}`
    }
  }

  console.log('跳转到就诊人编辑页面:', editUrl)

  wx.navigateTo({
    url: editUrl
  })
}
```

**功能特点**：
- 根据 `inquirerId` 判断跳转到编辑页面还是新增页面
- 传递视频咨询相关参数，确保编辑完成后能正确刷新数据

## 用户交互流程

### 1. 切换就诊人流程

```
用户点击选择就诊人
↓
调用 selectPatient 方法
↓
设置新的当前就诊人
↓
重置知情同意书状态
├─ checked: false
└─ isAgreement: false
↓
滚动到选中的就诊人位置
```

### 2. 勾选知情同意书流程

```
用户点击勾选知情同意书
↓
调用 onChecked 方法
↓
校验当前就诊人是否选择
├─ 未选择 → 提示"请先选择就诊人"
└─ 已选择 → 继续校验
↓
校验就诊人是否实名
├─ 未实名 → 显示实名认证提醒弹框
│   ├─ 用户选择"稍后处理" → 取消勾选
│   └─ 用户选择"去完善" → 跳转编辑页面
└─ 已实名 → 允许勾选
↓
设置勾选状态并显示协议弹框
```

### 3. 实名认证提醒弹框

**弹框内容**：
- **标题**：实名认证提醒
- **内容**：当前就诊人"XXX"尚未完成实名认证，请先完善身份证信息后再勾选知情同意书。
- **按钮**：
  - 取消按钮：稍后处理
  - 确认按钮：去完善

## 数据状态管理

### 页面数据状态

```javascript
data: {
  checked: false,        // 知情同意书勾选状态
  isAgreement: false,    // 协议同意状态
  currentPatient: null,  // 当前选中的就诊人
  currentPatientIndex: 0 // 当前选中的就诊人索引
}
```

### 状态变化时机

1. **页面初始化**：
   - `checked: false`
   - `isAgreement: false`

2. **切换就诊人**：
   - `checked: false` （重置）
   - `isAgreement: false` （重置）

3. **勾选知情同意书**：
   - 实名校验通过：`checked: true`
   - 实名校验失败：保持 `checked: false`

4. **同意协议**：
   - `checked: true`
   - `isAgreement: true`

5. **取消勾选**：
   - `checked: false`
   - `isAgreement: false`

## 实名认证判断标准

### 判断依据

使用 `currentPatient.idCard` 字段作为实名认证的判断标准：

```javascript
const hasIdCard = !!(currentPatient.idCard && currentPatient.idCard.trim())
```

### 判断逻辑

1. **已实名**：`idCard` 字段有值且去除空格后不为空
2. **未实名**：`idCard` 字段为空、null、undefined 或只包含空格

### 扩展性

如果将来有其他实名认证字段（如 `isRealNameAuth`），可以在 `validatePatientRealName` 方法中添加：

```javascript
// 可以添加其他实名认证判断条件
const hasIdCard = !!(currentPatient.idCard && currentPatient.idCard.trim())
const isRealNameAuth = currentPatient.isRealNameAuth === true
const isRealName = hasIdCard || isRealNameAuth
```

## 错误处理

### 1. 未选择就诊人

```javascript
if (!currentPatient) {
  wx.showToast({
    title: '请先选择就诊人',
    icon: 'none',
    duration: 3000
  })
  return false
}
```

### 2. 未实名认证

```javascript
if (!hasIdCard) {
  wx.showModal({
    title: '实名认证提醒',
    content: `当前就诊人"${currentPatient.name}"尚未完成实名认证，请先完善身份证信息后再勾选知情同意书。`,
    showCancel: true,
    cancelText: '稍后处理',
    confirmText: '去完善',
    success: (res) => {
      if (res.confirm) {
        this.editCurrentPatient()
      }
    }
  })
  return false
}
```

## 测试场景

### 1. 切换就诊人测试

**步骤**：
1. 选择就诊人A，勾选知情同意书
2. 切换到就诊人B

**预期结果**：
- 知情同意书勾选状态被重置为未勾选
- 协议同意状态被重置

### 2. 实名认证校验测试

#### 场景1：已实名就诊人
**步骤**：
1. 选择有身份证号码的就诊人
2. 点击勾选知情同意书

**预期结果**：
- 允许勾选
- 显示协议弹框

#### 场景2：未实名就诊人
**步骤**：
1. 选择没有身份证号码的就诊人
2. 点击勾选知情同意书

**预期结果**：
- 不允许勾选
- 显示实名认证提醒弹框

### 3. 编辑就诊人测试

**步骤**：
1. 选择未实名就诊人
2. 点击勾选知情同意书
3. 在弹框中选择"去完善"

**预期结果**：
- 跳转到就诊人编辑页面
- URL包含正确的参数

## 相关文件

- **主要文件**：`wxapp/pages_videoConsult/consultRoom/index.js`
- **修改方法**：
  - `selectPatient()` - 切换就诊人时重置协议状态
  - `onChecked()` - 勾选时校验实名状态
  - `validatePatientRealName()` - 新增实名校验方法
  - `editCurrentPatient()` - 新增编辑当前就诊人方法

## 总结

通过这次优化：

1. ✅ **状态重置**：切换就诊人时自动重置知情同意书状态
2. ✅ **实名校验**：只有实名认证的就诊人才能勾选协议
3. ✅ **用户引导**：未实名时提供明确的引导和操作入口
4. ✅ **状态一致**：确保协议状态与当前就诊人保持一致
5. ✅ **用户体验**：提供友好的提示和操作流程

现在用户在切换就诊人时会自动重置协议状态，并且只有完成实名认证的就诊人才能勾选知情同意书，确保了业务流程的合规性和用户体验的一致性。
