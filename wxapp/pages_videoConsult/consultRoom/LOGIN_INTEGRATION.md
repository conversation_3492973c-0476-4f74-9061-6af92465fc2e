# 视频咨询房间页面登录状态检查功能

## 功能概述

为视频咨询房间页面（`pages_videoConsult/consultRoom/index`）添加了登录状态检查功能，确保用户在未登录状态下访问该页面时，能够自动跳转到登录页面，登录成功后返回到原页面并保持所有参数。

## 实现原理

### 1. 登录状态检查

在页面的 `onLoad` 方法中添加登录状态检查：

```javascript
onLoad(options) {
  console.log('视频咨询室页面加载', options)
  
  // 保存页面参数，用于登录后恢复
  this.pageOptions = options
  
  // 检查用户登录状态
  if (!this.checkUserLogin()) {
    console.log('用户未登录，跳转到登录页面')
    this.redirectToLogin(options)
    return
  }
  
  console.log('用户已登录，继续初始化页面')
  this.initPageWithUserInfo(options)
}
```

### 2. 登录状态检查逻辑

```javascript
checkUserLogin() {
  const userInfo = app.globalData.userInfo
  const token = wx.getStorageSync('token')
  
  // 检查是否有用户信息和token
  return !!(userInfo && userInfo.userId && token)
}
```

### 3. 参数传递机制

当检测到用户未登录时，将当前页面的所有参数传递给登录页面：

```javascript
redirectToLogin(options) {
  try {
    console.log('跳转到登录页面，携带参数:', options)
    
    // 使用 util.goLogin 方法，传递页面参数
    util.goLogin(options)
    
  } catch (err) {
    console.error('跳转到登录页面失败:', err)
    // 如果参数处理失败，直接跳转到登录页面
    util.goLogin()
  }
}
```

### 4. 登录页面参数处理

登录页面接收并保存传递的参数：

```javascript
onLoad: function(options) {
  console.log(options, 'options')
  let params = options && options.params
  // 解码并反序列化 JSON 字符串为对象
  if (params) {
    let paramsObj = JSON.parse(decodeURIComponent(params))
    this.setData({ paramsObj }) 
  }
}
```

### 5. 登录成功后的页面恢复

登录成功后，检查是否有保存的页面参数，如果有则重新跳转到目标页面：

```javascript
redirectToTargetPage() {
  const paramsObj = this.data.paramsObj
  if (!paramsObj) {
    wx.switchTab({ url: '/pages/home/<USER>' })
    return
  }

  // 构建目标页面URL
  let targetUrl = '/pages_videoConsult/consultRoom/index'
  const params = []

  // 添加所有参数到URL
  Object.keys(paramsObj).forEach(key => {
    if (paramsObj[key] !== undefined && paramsObj[key] !== null) {
      params.push(`${key}=${encodeURIComponent(paramsObj[key])}`)
    }
  })

  if (params.length > 0) {
    targetUrl += '?' + params.join('&')
  }

  wx.redirectTo({ url: targetUrl })
}
```

## 使用场景

### 场景1: 直接访问视频咨询房间

用户通过外部链接或其他方式直接访问视频咨询房间页面：

```
/pages_videoConsult/consultRoom/index?consultId=123&doctorId=456&subOrderCode=ABC&packageCode=DEF
```

**流程：**
1. 页面加载，检测到用户未登录
2. 自动跳转到登录页面，携带所有参数
3. 用户完成登录
4. 自动返回到视频咨询房间页面，所有参数保持不变

### 场景2: 从其他页面跳转

用户从其他页面跳转到视频咨询房间，但登录状态已过期：

**流程：**
1. 页面加载，检测到登录状态过期
2. 自动跳转到登录页面
3. 用户重新登录
4. 返回到视频咨询房间页面，继续原有流程

## 技术特点

### 1. 无缝用户体验

- 用户无需手动重新输入参数
- 登录后自动恢复到原始状态
- 支持所有类型的页面参数

### 2. 参数完整性保证

- 支持字符串、数字、布尔值等各种参数类型
- 自动处理URL编码和解码
- 防止参数丢失或损坏

### 3. 错误处理

- 完善的异常处理机制
- 参数序列化失败时的降级处理
- 页面跳转失败时的备用方案

### 4. 兼容性

- 兼容现有的登录流程
- 不影响其他页面的登录逻辑
- 支持多种页面跳转方式

## 测试建议

### 1. 基础功能测试

- 未登录状态下直接访问页面
- 登录状态过期后访问页面
- 各种参数组合的传递测试

### 2. 参数保持测试

- 简单参数（字符串、数字）
- 复杂参数（包含特殊字符）
- 空参数和undefined参数

### 3. 异常情况测试

- 网络异常时的处理
- 参数序列化失败的处理
- 页面跳转失败的处理

## 注意事项

1. **参数长度限制**：URL参数有长度限制，如果参数过多可能需要考虑其他传递方式

2. **安全性**：敏感参数不应通过URL传递，建议使用其他安全的存储方式

3. **性能考虑**：频繁的页面跳转可能影响用户体验，建议优化登录状态检查的频率

4. **兼容性**：确保在不同版本的微信小程序中都能正常工作

## 测试用例

### 测试用例1: 未登录用户直接访问

**测试步骤：**
1. 清除小程序缓存（删除token和userInfo）
2. 直接访问：`/pages_videoConsult/consultRoom/index?consultId=123&doctorId=456&subOrderCode=ABC&packageCode=DEF`
3. 完成登录流程

**期望结果：**
- 自动跳转到登录页面
- 登录成功后返回到视频咨询房间
- 所有参数（consultId、doctorId、subOrderCode、packageCode）保持不变
- 页面正常初始化并调用相关接口

### 测试用例2: 登录状态过期

**测试步骤：**
1. 用户已登录状态
2. 手动清除token（模拟登录过期）
3. 访问视频咨询房间页面

**期望结果：**
- 检测到登录状态异常，自动跳转到登录页面
- 重新登录后返回原页面
- 页面状态和参数完整恢复

### 测试用例3: 复杂参数传递

**测试步骤：**
1. 使用包含特殊字符的参数访问页面
2. 参数示例：`consultId=123&doctorId=456&subOrderCode=ABC-123&packageCode=DEF_456&from=history&status=waiting`

**期望结果：**
- 所有参数正确传递和恢复
- 特殊字符正确处理
- 页面逻辑根据参数正确执行

## 代码关键点

### 1. 登录状态检查

```javascript
checkUserLogin() {
  const userInfo = app.globalData.userInfo
  const token = wx.getStorageSync('token')
  return !!(userInfo && userInfo.userId && token)
}
```

### 2. 参数传递

```javascript
// 视频咨询房间页面
util.goLogin(options)

// util.js
function goLogin(pageParams = null) {
  let loginUrl = '/pages/auth/login/login'
  if (pageParams) {
    const paramsString = encodeURIComponent(JSON.stringify(pageParams))
    loginUrl += `?params=${paramsString}`
  }
  wx.navigateTo({ url: loginUrl })
}
```

### 3. 登录后恢复

```javascript
// 登录页面
redirectToTargetPage() {
  const paramsObj = this.data.paramsObj
  let targetUrl = '/pages_videoConsult/consultRoom/index'

  // 构建完整URL
  Object.keys(paramsObj).forEach(key => {
    if (paramsObj[key] !== undefined && paramsObj[key] !== null) {
      params.push(`${key}=${encodeURIComponent(paramsObj[key])}`)
    }
  })

  wx.redirectTo({ url: targetUrl })
}
```

## 总结

这个功能实现了视频咨询房间页面的登录状态检查和参数保持，确保用户在任何情况下都能获得流畅的使用体验，同时保证了数据的完整性和安全性。

**主要特点：**
- ✅ 自动检测登录状态
- ✅ 无缝参数传递和恢复
- ✅ 完善的错误处理
- ✅ 兼容现有登录流程
- ✅ 支持各种访问场景
