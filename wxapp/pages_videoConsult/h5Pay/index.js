Page({
  data: {},
  onLoad(options) {
    console.log('支付参数', JSON.parse(decodeURIComponent(options.payParams)))
    console.log('支付成功重定向地址', decodeURIComponent(options.redirectUrl))
    wx.requestPayment({
      ...JSON.parse(decodeURIComponent(options.payParams)),
      success: () => {
        wx.showLoading({
          icon: 'loading',
          title: '检查支付结果...'
        })
        setTimeout(() => {
          wx.hideLoading()
          wx.redirectTo({
            url: '/pages/webview/index?url=' + options.redirectUrl,
            fail: (e) => {
              console.log('支付成功但页面跳转失败', e)
              wx.navigateBack()
            }
          })
        }, 5000)
      },
      fail: (e) => {
        wx.navigateBack()
      }
    })
  }
})
