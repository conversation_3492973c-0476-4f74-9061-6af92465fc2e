# 视频问诊未读消息计算修复

## 问题描述

小助理消息和其他视频问诊消息（`consultType=2`）被错误地计算到未读消息数量中，导致用户在首页和问诊列表页面看到不必要的未读消息提示。

### 问题根源

1. **消息记录逻辑问题**：
   - 在 `onMessageArrived` 方法中，所有消息（包括视频问诊消息）都被添加到 `messageRecord` 中
   - `getMessageNum` 方法计算所有 `messageRecord` 中的未读消息，没有区分消息类型

2. **调用场景**：
   - 首页（`pages/home/<USER>
   - 问诊列表页（`pages/consult/index/index`）调用 `app.getMessageNum()`
   - 用户页面（`pages/user/user`）调用 `app.getMessageNum()`

## 修复方案

### 核心思路

**在消息记录阶段就过滤掉视频问诊消息**，而不是在计算阶段过滤。这样确保 `messageRecord` 中只包含图文问诊的未读消息。

### 修改内容

#### 修改前的逻辑：
```javascript
len = util.getChatData(chatkey).length
const messageRecord = util.getChatData('messageRecord') ? util.getChatData('messageRecord') : {}
const kay = message.content?.specificMessageType ? message.to.id : message.from?.id
messageRecord[kay] = messageRecord[kay] ? messageRecord[kay] + 1 : 1

if (!message.content?.specificMessageType) {
  wx.setStorageSync('messageRecord', messageRecord)

  // 检查是否为视频问诊消息，如果是则不计算未读消息
  if (isVideoConsult) {
    console.log('收到视频问诊消息(consultType=2)，跳过未读消息计算:', message)
  } else {
    // 只有非视频问诊消息才计算未读消息
    this.getMessageNum()
  }
}
```

**问题**：视频问诊消息仍然被添加到 `messageRecord` 中，只是在计算时跳过。

#### 修改后的逻辑：
```javascript
len = util.getChatData(chatkey).length

// 只有非视频问诊消息才记录到 messageRecord 中
if (!isVideoConsult && !message.content?.specificMessageType) {
  const messageRecord = util.getChatData('messageRecord') ? util.getChatData('messageRecord') : {}
  const kay = message.from?.id
  messageRecord[kay] = messageRecord[kay] ? messageRecord[kay] + 1 : 1
  
  wx.setStorageSync('messageRecord', messageRecord)
  console.log('记录图文问诊未读消息:', {
    doctorId: kay,
    count: messageRecord[kay],
    messageType: message.type
  })
  
  // 计算未读消息
  this.getMessageNum()
} else if (isVideoConsult) {
  console.log('收到视频问诊消息，跳过未读消息记录:', {
    type: message.type,
    consultType: message.consultType,
    consultId: message.consultId
  })
}
```

**优势**：视频问诊消息根本不会被添加到 `messageRecord` 中。

## 修复逻辑

### 1. 消息分类

```
消息到达
↓
判断 consultType === 2？
├─ 是 → 视频问诊消息
│   ├─ 不记录到 messageRecord
│   ├─ 不计算未读消息
│   └─ 只分发给视频咨询室页面
└─ 否 → 图文问诊消息
    ├─ 记录到 messageRecord
    ├─ 计算未读消息
    └─ 分发给图文咨询页面
```

### 2. 消息记录条件

只有同时满足以下条件的消息才会被记录到 `messageRecord`：

1. **非视频问诊消息**：`!isVideoConsult`（即 `consultType !== 2`）
2. **非特殊消息类型**：`!message.content?.specificMessageType`

### 3. 未读消息计算

`getMessageNum` 方法保持不变，因为现在 `messageRecord` 中只包含需要计算的图文问诊未读消息。

## 消息类型处理

### 需要记录未读的消息

1. **图文问诊消息**：
   - `consultType`: 1 或 null 或 undefined
   - 普通的文本、图片、语音等消息
   - 医生回复的病历、处方等消息

### 不需要记录未读的消息

1. **视频问诊消息**：
   - `consultType`: 2
   - 包括小助理消息（type=10015, 10016）
   - 包括医生在视频问诊中的所有消息

2. **特殊消息类型**：
   - `message.content?.specificMessageType` 存在的消息

## 调试信息

### 修复后的日志输出

```javascript
// 图文问诊消息被记录
"记录图文问诊未读消息: {doctorId: '123', count: 1, messageType: 1}"

// 视频问诊消息被跳过
"收到视频问诊消息，跳过未读消息记录: {type: 10015, consultType: 2, consultId: 'default'}"

// 小助理消息被跳过
"收到视频问诊消息，跳过未读消息记录: {type: 10015, consultType: 2, consultId: 'default'}"
```

## 测试场景

### 1. 小助理问候语消息

```javascript
const assistantMessage = {
  type: 10015,
  consultType: 2,
  consultId: 'default',
  content: '我是您的招商信诺助理-小诺',
  from: { id: 'assistant' }
}

// 预期结果：
// - 不记录到 messageRecord ✅
// - 不计算未读消息 ✅
// - 只分发给视频咨询室页面 ✅
```

### 2. 视频问诊医生消息

```javascript
const doctorVideoMessage = {
  type: 1,
  consultType: 2,
  consultId: '123456',
  content: '医生在视频问诊中的回复',
  from: { id: 'doctor123' }
}

// 预期结果：
// - 不记录到 messageRecord ✅
// - 不计算未读消息 ✅
// - 只分发给视频咨询室页面 ✅
```

### 3. 图文问诊医生消息

```javascript
const doctorTextMessage = {
  type: 1,
  consultType: 1,
  content: '医生在图文问诊中的回复',
  from: { id: 'doctor123' }
}

// 预期结果：
// - 记录到 messageRecord ✅
// - 计算未读消息 ✅
// - 分发给图文咨询页面 ✅
```

### 4. 特殊消息类型

```javascript
const specialMessage = {
  type: 1,
  consultType: 1,
  content: { specificMessageType: 'special' },
  from: { id: 'doctor123' }
}

// 预期结果：
// - 不记录到 messageRecord ✅
// - 不计算未读消息 ✅
```

## 页面调用场景

### 1. 首页（pages/home/<USER>

```javascript
// onShow 方法中调用
app.getMessageNum()

// 现在只会计算图文问诊的未读消息
// 视频问诊消息不会影响首页的未读提示
```

### 2. 问诊列表页（pages/consult/index/index）

```javascript
// getMsgDetail 方法中调用
app.getMessageNum()

// getAppIdAndKey 方法中调用
app.getMessageNum()

// 现在只会计算图文问诊的未读消息
// 视频问诊消息不会影响问诊列表的未读提示
```

### 3. 用户页面（pages/user/user）

```javascript
// 页面显示时调用
app.getMessageNum()

// 现在只会计算图文问诊的未读消息
```

## 数据结构

### messageRecord 结构

修复后，`messageRecord` 的结构保持不变，但内容更纯净：

```javascript
{
  "doctor123": 2,    // 图文问诊医生123有2条未读消息
  "doctor456": 1,    // 图文问诊医生456有1条未读消息
  // 不包含任何视频问诊相关的未读消息
}
```

### 存储位置

- **本地存储**：`wx.setStorageSync('messageRecord', messageRecord)`
- **内存缓存**：`util.getChatData('messageRecord')`

## 相关代码位置

- **文件**：`wxapp/app.js`
- **方法**：`onMessageArrived()`
- **行号**：265-288（修改后的消息记录逻辑）

## 业务影响

### 修复前

- ❌ 视频问诊消息被计算到未读消息中
- ❌ 小助理消息被计算到未读消息中
- ❌ 用户看到不必要的未读消息提示
- ❌ 影响用户体验和判断

### 修复后

- ✅ 只有图文问诊消息被计算未读
- ✅ 视频问诊消息完全不影响未读计算
- ✅ 用户只看到真正需要关注的未读消息
- ✅ 提升用户体验和准确性

## 注意事项

### 1. 向后兼容性

- 修改保持了对现有消息处理逻辑的兼容性
- 只是改变了消息记录的条件，不影响其他功能

### 2. 数据一致性

- 确保 `messageRecord` 中的数据与实际的图文问诊未读消息一致
- 视频问诊消息不会"污染"未读消息记录

### 3. 性能优化

- 减少了不必要的消息记录操作
- 提高了未读消息计算的准确性

## 总结

通过这次修复：

1. ✅ **准确分类**：正确区分视频问诊和图文问诊消息
2. ✅ **精确记录**：只记录需要计算未读的消息
3. ✅ **准确计算**：未读消息计算结果更准确
4. ✅ **用户体验**：避免不必要的未读消息提示
5. ✅ **代码清晰**：逻辑更清晰，便于维护

现在用户在首页和问诊列表页面只会看到图文问诊的真实未读消息数量，视频问诊相关的消息不会干扰未读消息的计算。
