# 药品订单高济H5集成功能

## 功能概述

为用户页面的"药品订单"功能添加了高济H5集成，用户点击药品订单时，会先调用接口获取高济H5地址，然后跳转到高济H5页面进行药品订单管理。

## 接口定义

### API配置

在 `wxapp/config/api.js` 中新增接口定义：

```javascript
getMedicalOrderUrl: WxApiRoot + 'medical/b2c/gao/ji/orders/pre/order/info/sync' //post 药品订单跳转到高济h5获取地址
```

### 接口说明

- **接口地址**: `medical/b2c/gao/ji/orders/pre/order/info/sync`
- **请求方法**: POST
- **入参**: 无
- **返回数据**: 
  ```json
  {
    "code": 0,
    "msg": "success",
    "data": {
      "jumpUrl": "https://gaoji.h5.url/orders"
    }
  }
  ```

## 功能实现

### 页面位置

- **文件路径**: `wxapp/pages/user/user.js`
- **方法名**: `medicalOrder()`
- **触发方式**: 用户点击"药品订单"按钮

### 核心代码

```javascript
// 药品订单-跳转高济h5页面
async medicalOrder() {
  try {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo || !userInfo.token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.toLogin()
      return
    }

    util.showLoading({
      title: '获取地址中...',
      mask: true
    })

    // 调用接口获取高济H5地址
    const { data } = await util.request(api.getMedicalOrderUrl, {}, 'post', 2)

    console.log('药品订单接口原始响应:', data)
    console.log('格式化后的数据:', data.data)

    util.hideLoading()

    if (data.code === 0 && data.data && data.data.jumpUrl) {
      // 跳转到高济H5页面
      wx.navigateTo({
        url: `/pages/webView/index?url=${encodeURIComponent(data.data.jumpUrl)}`
      })
    } else {
      // 如果接口失败，降级到原有的订单列表页面
      console.log('获取高济H5地址失败，降级到订单列表页面')
      wx.navigateTo({
        url: '/pages/orderList/orderList'
      })
    }
  } catch (error) {
    console.error('药品订单接口调用失败:', error)
    util.hideLoading()
    
    // 网络异常时，降级到原有的订单列表页面
    console.log('网络异常，降级到订单列表页面')
    wx.navigateTo({
      url: '/pages/orderList/orderList'
    })
  }
}
```

## 功能特点

### 1. 登录状态检查

- 在调用接口前检查用户登录状态
- 未登录时自动跳转到登录页面
- 确保接口调用的安全性

### 2. 加载状态提示

- 显示"获取地址中..."的加载提示
- 提升用户体验，让用户知道正在处理

### 3. 调试日志

- 添加详细的console.log用于调试
- 输出原始响应和格式化数据
- 便于问题排查和调试

### 4. 错误处理和降级机制

- **接口失败降级**: 如果获取高济H5地址失败，自动降级到原有的订单列表页面
- **网络异常降级**: 网络异常时也会降级到原有页面
- **用户体验保障**: 确保在任何情况下用户都能访问订单功能

### 5. URL编码处理

- 对跳转URL进行正确的编码处理
- 防止URL参数冲突和特殊字符问题

## 用户流程

### 正常流程

1. 用户在个人中心页面点击"药品订单"
2. 系统检查用户登录状态
3. 显示加载提示"获取地址中..."
4. 调用接口获取高济H5地址
5. 成功获取地址后跳转到高济H5页面
6. 用户在高济H5页面进行药品订单管理

### 异常流程

#### 未登录用户

1. 用户点击"药品订单"
2. 检测到未登录状态
3. 显示"请先登录"提示
4. 自动跳转到登录页面

#### 接口失败

1. 用户点击"药品订单"
2. 接口调用失败或返回错误
3. 自动降级到原有的订单列表页面
4. 用户可以继续使用原有功能

#### 网络异常

1. 用户点击"药品订单"
2. 网络请求异常
3. 自动降级到原有的订单列表页面
4. 确保功能可用性

## 技术优势

### 1. 渐进式升级

- 保留原有功能作为降级方案
- 新功能失败时不影响用户使用
- 平滑的功能升级体验

### 2. 完善的错误处理

- 多层次的错误处理机制
- 详细的错误日志记录
- 用户友好的错误提示

### 3. 安全性保障

- 登录状态验证
- 接口调用权限控制
- 防止未授权访问

### 4. 可维护性

- 清晰的代码结构
- 详细的注释说明
- 便于后续维护和扩展

## 测试建议

### 1. 功能测试

- 已登录用户点击药品订单
- 未登录用户点击药品订单
- 接口返回成功的情况
- 接口返回失败的情况

### 2. 异常测试

- 网络断开时的处理
- 接口超时的处理
- 返回数据格式异常的处理

### 3. 兼容性测试

- 不同版本微信的兼容性
- 不同设备的兼容性
- 原有功能的兼容性

## 配置要求

### 1. 后端接口

- 确保接口 `medical/b2c/gao/ji/orders/pre/order/info/sync` 已部署
- 接口返回格式符合预期
- 接口性能满足要求

### 2. 高济H5页面

- 确保高济H5页面可正常访问
- H5页面支持小程序webview环境
- 页面功能完整可用

### 3. 小程序配置

- webview域名已添加到小程序后台
- 相关权限已配置
- 页面路径已正确注册

## 总结

这个功能实现了药品订单与高济H5的无缝集成，在提供新功能的同时保证了系统的稳定性和用户体验。通过完善的错误处理和降级机制，确保用户在任何情况下都能正常使用药品订单功能。
