// pages/user/user.js
const api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imgObject: {
      bg_my: api.ImgUrl + 'images/video_consult/<EMAIL>',
      ic_my_more: api.ImgUrl + 'images/ic_my_more.png',
      // ic_my_service: api.ImgUrl + 'images/ic_my_service.png',
      // ic_my_patient: api.ImgUrl + 'images/ic_my_patient.png',
      // ic_my_prescription: api.ImgUrl + 'images/ic_my_prescription.png',
      // ic_my_address: api.ImgUrl + 'images/ic_my_address.png',
      ic_more_grey: api.ImgUrl + 'images/ic_more_grey.png',
      // ic_my_follow: api.ImgUrl + 'images/ic_my_follow.png',
      nomes: api.ImgUrl + 'images/nomes.png',
      ic_my_consultation: api.ImgUrl + 'images/ic_my_consultation.png',
      ic_order_medicine: api.ImgUrl + 'images/video_consult/<EMAIL>', // 药品订单icon
      bg_order_medicine: api.ImgUrl + 'images/video_consult/<EMAIL>', // 药品订单背景
      ic_order_consultation: api.ImgUrl + 'images/video_consult/<EMAIL>', // 问诊订单icon
      bg_order_consultation: api.ImgUrl + 'images/video_consult/<EMAIL>', // 问诊订单背景
      ic_order_more: api.ImgUrl + 'images/video_consult/ic_order_more@2x(1).png', // 订单更多icon
      ic_my_patient: api.ImgUrl + 'images/video_consult/<EMAIL>', // 就诊人管理icon
      ic_my_health_records: api.ImgUrl + 'images/video_consult/<EMAIL>', // 健康档案icon
      ic_my_follow: api.ImgUrl + 'images/video_consult/<EMAIL>', // 随访计划icon
      ic_my_prescription: api.ImgUrl + 'images/video_consult/<EMAIL>', // 我的处方icon
      ic_my_address: api.ImgUrl + 'images/video_consult/<EMAIL>', // 地址icon
      ic_my_service: api.ImgUrl + 'images/video_consult/<EMAIL>' // 在线客服icon
    },
    userName: null,
    userPhone: null,
    userAvatar: null,
    orderMenu: [{
      icon: api.ImgUrl + 'images/ic_my_paid.png',
      name: '待支付',
      num: 0,
      type: 1
    }, {
      icon: api.ImgUrl + 'images/ic_my_delivered.png',
      name: '待发货',
      num: 0,
      type: 2
    }, {
      icon: api.ImgUrl + 'images/ic_my_receiving.png',
      name: '待收货',
      num: 0,
      type: 3
    }, {
      icon: api.ImgUrl + 'images/ic_my_cancle.png',
      name: '已结束',
      num: 0,
      type: 5
    }],
    isBack: false,
    csPhone: '',
    isLogin: false
  },
  // 药品订单-跳转高济h5页面
  medicalOrder() {
    wx.navigateTo({
      url: '/pages/orderList/orderList'
    })
  },

  // 问诊订单
  consultOrder() {
    wx.navigateTo({
      url: '/pages/consult/order/list/index'
    })
  },
  call() {
    wx.makePhoneCall({
      phoneNumber: this.data.csPhone
    })
  },

  /**
   * 处理健康档案点击事件
   */
  async handleHealthRecords() {
    try {
      // 检查登录状态
      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo.token) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        this.toLogin()
        return
      }

      util.showToast({
        title: '加载中...',
        icon: 'loading'
      })

      // 调用后端接口获取高济健康档案H5地址
      const response = await util.request(api.getHealthRecordsUrl, { patientId: app.globalData.userInfo.userId }, 'get')

      util.hideToast()

      if (response.data.code === 0) {
        const h5Url = response.data.data
        if (h5Url) {
          console.log('获取到高济健康档案H5地址:', h5Url)

          // 跳转到高济健康档案H5页面
          wx.navigateTo({
            url: `/pages/webView/index?url=${encodeURIComponent(h5Url)}`
          })
        } else {
          util.showToast({
            title: '获取健康档案地址失败',
            icon: 'none'
          })
        }
      } else {
        util.showToast({
          title: response.data.msg || '获取健康档案地址失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取高济健康档案H5地址失败:', error)
      util.hideToast()
      util.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      })
    }
  },
  // 订单数量
  async statisticalInfo() {
    var userInfo = await util.getBaseInfo()
    console.log(userInfo, '======userInfo=====')
    this.setData({
      userAvatar: userInfo.photo,
      userName: userInfo.name,
      userPhone: util.stringHidden(userInfo.phone, 3, 4)
    })
    util.request(api.statisticalInfo)
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            ['orderMenu[' + 0 + '].num']: res.data.data.pendingNum,
            ['orderMenu[' + 1 + '].num']: res.data.data.transferSuccessNum,
            ['orderMenu[' + 2 + '].num']: res.data.data.shippingNum
          })
        }
      })
      .catch(res => {})

  },
  getPhoneNum() {
    util.request(api.getPhoneNum, {}, 'get')
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            csPhone: res.data.data.csPhone
          })
        }
      })
      .catch(res => {})
  },
  // 上传头像
  onChooseAvatar(e) {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.toLogin()
      return
    }

    const { avatarUrl } = e.detail
    const sysInfo = wx.getSystemInfoSync()
    util.showLoading({
      title: '上传中~',
      mask: true
    })
    wx.uploadFile({
      url: api.uplodPhoto,
      filePath: avatarUrl,
      name: 'file',
      header: {
        'content-type': 'multipart/form-data',
        'Authorization': wx.getStorageSync('token'),
        '_m': sysInfo.model,
        '_o': 0,
        '_w': 1
      },
      success: (res) => {
        var data = JSON.parse(res.data)
        const userInfo = wx.getStorageSync('userInfo')
        userInfo.avatar = data.data
        util.setUserInfo(userInfo)
        this.setData({
          userAvatar: data.data
        })
      },
      complete: () => {
        util.hideLoading()
      }
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.getPhoneNum()
    }
  },
  // 打开企业微信客服聊天
  handleOpenServiceChat() {
    wx.openCustomerServiceChat({
      extInfo: {
        url: 'https://work.weixin.qq.com/kfid/kfc78baf37080201066'
      },
      corpId: 'ww76dc890247c546fd',
      success(res) {
        console.log(res, 'res')
      }
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    // 检查登录状态
    const userInfo = wx.getStorageSync('userInfo')
    this.setData({
      isLogin: !!userInfo
    })
    if (userInfo) {
      this.statisticalInfo()
    }
  },
  handleContact(e) {
    console.log(e.detail.path)
    console.log(e.detail.query)
  },
  handleOpenJD() {
    wx.navigateToMiniProgram({
      appId: 'wx91d27dbf599dff74',
      path: 'pages/gold/item/pages/detail/index?sku=10054390446190',
      extraData: {
        foo: 'bar'
      },
      envVersion: 'release',
      success(res) {
        // 打开成功
      }
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  // 添加登录跳转方法
  toLogin() {
    wx.navigateTo({
      url: '/pages/auth/login/login' // 跳转到登录页面
    })
  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function() {

  // }
})
