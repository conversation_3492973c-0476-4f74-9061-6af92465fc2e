var api = require('../../config/api.js')
var util = require('../../utils/util')
var Config = require('../../config/index.js')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    ImgUrl: api.ImgUrl,
    isBack: false,
    statusBarHeight: null,
    logoHeight: null,
    imgObject: {
      search: api.ImgUrl + 'images/<EMAIL>', //搜索图标
      menu1: api.ImgUrl + 'images/bg_home_expert.png', //专家名医
      menu2: api.ImgUrl + 'images/img_video_consultation.png', //复诊开药
      bgImg: api.ImgUrl + 'images/bg_home.png', //底部背景图
      logo: api.ImgUrl + 'logo/ic_text.png', //logo
      img_blank_doctor: api.ImgUrl + 'images/img_blank_doctor.png',
      ic_font_message: api.ImgUrl + 'images/ic_font_message.png',
      bg_home: api.ImgUrl + 'images/video_consult/<EMAIL>', // 首页背景图
      ic_quick_consultation: api.ImgUrl + 'images/video_consult/<EMAIL>', // 快速咨询
      ic_video_consultation: api.ImgUrl + 'images/video_consult/<EMAIL>', // 全科视频问诊
      ic_hospitalization_guidance: api.ImgUrl + 'images/video_consult/<EMAIL>', // 小诺导诊
      ic_department_navigation: api.ImgUrl + 'images/video_consult/<EMAIL>' // 科室导航
    },
    noData: false,
    imgUrls: [
      api.ImgUrl + 'images/img_home_banner.png',
      api.ImgUrl + 'images/img_home_banner.png'
    ],
    indicatorDots: false,
    autoplay: true,
    interval: 3000,
    duration: 500, //滑动动画时长
    circular: true,
    indicatorColor: 'rgba(255,255,255,1)', //普通轮播点背景色
    indicatorActiveColor: '#2e9cff', //选中轮播点背景色
    swiperCurrent: 0,
    list: [],
    listQuery: {
      page: 1 // 页码
    },
    templateId: [],
    clickFlag: true,
    doctorName: '',
    doctorId: null,
    type: null,
    isLogin: false,
    tapTime: '',
    company: Config.company,
    navTabList: [{ title: '我的医生' }, { title: '医学资讯' }],
    currentNav: 0,
    container: null,
    isFixed: false,
    // 资讯
    artCurrent: 0,
    artList: [],
    artListQuery: {
      page: 1, // 页码
      type: 2,
      groupId: 1
    },
    artHasNext: false,
    docHasNext: false,
    departments: [], //
    noticeList: [], //院务公开最新一条
    bannerList: [],
    commonDiseaseList: [], //常见疾病列表
    defaultBanner: {
      id: -1, // 使用特殊ID标识默认banner
      bannerUrl: 'https://patient.xiaonuohealth.com/images/banner-ai.png',
      bannerType: 'AI', // 特殊类型标识AI banner
      targetUrl: '' // 可以设置默认跳转链接
    },
    showGetPhone: false,
    pendingBannerData: null,
    isLoggedIn: false,
    hasPhone: false,
    // URL scheme中转相关
    showRedirectPage: false,
    redirectCountdown: 2,
    redirectParams: null
  },

  // URL scheme中转倒计时
  startRedirectCountdown() {
    const timer = setInterval(() => {
      const countdown = this.data.redirectCountdown - 1
      this.setData({
        redirectCountdown: countdown
      })

      if (countdown <= 0) {
        clearInterval(timer)
        this.redirectToVideoConsult()
      }
    }, 1000)
  },

  // 跳转到视频咨询页面
  redirectToVideoConsult() {
    const { packageCode, subOrderCode } = this.data.redirectParams
    console.log('正在跳转到视频咨询页面，参数:', { packageCode, subOrderCode })

    wx.redirectTo({
      url: `/pages_videoConsult/consultRoom/index?packageCode=${packageCode}&subOrderCode=${subOrderCode}`,
      fail: (err) => {
        console.error('跳转失败:', err)
        // 如果跳转失败，可以尝试使用navigateTo
        wx.navigateTo({
          url: `/pages_videoConsult/consultRoom/index?packageCode=${packageCode}&subOrderCode=${subOrderCode}`
        })
      }
    })
  },

  // 立即跳转（用户点击按钮）
  handleImmediateRedirect() {
    this.redirectToVideoConsult()
  },

  // 常见疾病跳转
  handleDiseaseNavigator(e) {
    const { diseaseid, diseasename } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/famousDoctor/famousDoctor?diseaseId=${diseaseid}&diseaseName=${diseasename}`
    })
  },

  // 轮播图事件
  swiperChange(e) {
    const current = e.detail.current
    this.setData({
      swiperCurrent: current
    })
  },
  goSearchPage() {
    wx.navigateTo({
      url: '/pages/search/search'
    })
  },

  // 快速咨询
  handleQuickConsult() {
    wx.navigateTo({
      url: '/pages/famousDoctor/famousDoctor?labelId=1'
    })
  },

  // 科室导航
  handleDepartmentNavigation() {
    wx.navigateTo({
      url: '/pages/departments/index'
    })
  },

  // 全科视频问诊
  async handleVideoConsult() {
    const token = wx.getStorageSync('token')

    // 检查是否登录
    if (!token) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      })
      return
    }

    // 如果已登录但没有手机号,触发获取手机号
    if (!this.data.hasPhone) {
      // 存储当前操作类型,用于获取手机号后的跳转
      this.setData({
        pendingBannerData: { type: 'VIDEO_CONSULT', id: -1, url: '' }
      })
      return
    }

    // 已登录且有手机号,直接跳转H5
    this.handleVideoConsultNavigation()
  },

  // 小诺导诊
  async handleHospitalizationGuidance() {
    const token = wx.getStorageSync('token')

    // 检查是否登录
    if (!token) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      })
      return
    }

    // 如果已登录但没有手机号,触发获取手机号
    if (!this.data.hasPhone) {
      // 存储当前操作类型,用于获取手机号后的跳转
      this.setData({
        pendingBannerData: { type: 'AI', id: -1, url: '' }
      })
      return
    }

    // 已登录且有手机号,直接跳转AI
    this.handleAINavigation()
  },

  // 处理AI跳转逻辑
  async handleAINavigation() {
    // 获取带有缓存策略的token
    const tokenData = await this.getAIToken()
    if (!tokenData) {
      return
    }

    const { token } = tokenData
    let baseUrl = ''
    let projectCode = ''
    // 根据小程序版本设置不同的baseUrl
    const { envVersion } = wx.getAccountInfoSync().miniProgram

    if (envVersion === 'develop') {
      projectCode = 'CONSULT24123015231400163'
      baseUrl = 'https://ewphlp-uat.cignacmbhealth.com' // SIT环境
    } else if (envVersion === 'trial') {
      projectCode = 'CONSULT24123015231400163'
      baseUrl = 'https://ewphlp-uat.cignacmbhealth.com' // UAT环境
    } else {
      projectCode = 'CONSULT25031116305100191'
      baseUrl = 'https://cignacmbhealth.com' // 生产环境
    }
    // 获取完整的AI跳转链接
    const aiUrl = `${baseUrl}/mb-web/medical-guide/#/index/${projectCode}?token=${token}`
    wx.navigateTo({
      url: `/pages/webView/index?url=${encodeURIComponent(aiUrl)}`
    })
  },

  // 处理全科视频问诊跳转逻辑
  async handleVideoConsultNavigation() {
    try {
      util.showToast({
        title: '加载中...',
        icon: 'loading'
      })

      // 调用后端接口获取全科视频问诊完整H5地址
      const response = await util.request(api.getVideoConsultUrl, { patientId: app.globalData.userInfo.userId }, 'get')

      util.hideToast()

      if (response.data.code === 0) {
        const h5Url = response.data.data
        if (h5Url) {
          console.log('获取到全科视频问诊完整H5地址:', h5Url)

          // 后端返回完整地址，直接跳转
          wx.navigateTo({
            url: `/pages/webView/index?url=${encodeURIComponent(h5Url)}`
          })
          // wx.navigateTo({
          //   url: `/pages_videoConsult/consultRoom/index?subOrderCode=IC20240019151207&packageCode=PA003113`
          // })
        } else {
          util.showToast({
            title: '获取服务地址失败',
            icon: 'none'
          })
        }
      } else {
        util.showToast({
          title: response.data.msg || '获取服务地址失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('获取全科视频问诊H5地址失败:', error)
      util.hideToast()
      util.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('首页onLoad接收到的参数:', options)

    // 检查是否是URL scheme中转
    if (options.packageCode && options.subOrderCode) {
      console.log('检测到URL scheme参数，启动中转页面')
      this.setData({
        showRedirectPage: true,
        redirectParams: {
          packageCode: options.packageCode,
          subOrderCode: options.subOrderCode
        }
      })
      this.startRedirectCountdown()
      return
    }

    // LOGO位置设置
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight,
      logoHeight: app.globalData.navBarHeight - app.globalData.statusBarHeight
    })
    this.getDdepartments()
    this.fetchDiseaseList()
  },
  // 咨询记录
  counselHistory(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/consult/record/index?doctorId=' + id
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    this.setData({
      artHasNext: false,
      docHasNext: false,
      ['listQuery.page']: 1,
      ['artListQuery.page']: 1
    })
    this.getBannerList()
    var token = wx.getStorageSync('token')
    if (token !== '') {
      const data = await this.getMyDoctor(1)
      this.setData(
        {
          isLogin: data ? true : false
        },
        () => {
          this.getartList()
        }
      )
    } else {
      this.setData({
        isLogin: false
      })
    }
    this.getList()
    app.getMessageNum()

    // 获取登录状态和手机号状态
    const userInfo = wx.getStorageSync('userInfo') || {}
    const hasPhone = userInfo.phone && userInfo.phone.length > 0
    this.setData({
      isLoggedIn: !!token,
      hasPhone: hasPhone
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    var that = this
    if (!that.data.isLogin) {
      wx.stopPullDownRefresh()
      return false
    }
    that.getMyDoctor(1)
    that.getartList()
    that.getList()
    that.fetchDiseaseList()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    const { currentNav, artHasNext, docHasNext } = this.data
    if (currentNav === 0 && docHasNext) {
      this.data.listQuery.page++
      this.getMyDoctor(2)
    }
    if (currentNav === 1 && artHasNext) {
      this.data.artListQuery.page++
      this.getartList()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {},
  async getMyDoctor(type) {
    if (type !== 1 && type !== 2) {
      type = 1
    }
    var that = this
    try {
      const { data } = await util.request(
        api.myDoctor,
        that.data.listQuery,
        'post',
        '1',
        'false'
      )
      if (!data) {
        return false
      }
      const result = data.data
      console.log(result, '======data======')
      if (data.code === 0) {
        that.setData({
          list:
            type === 1 ? result.result : that.data.list.concat(result.result),
          docHasNext: result.hasNext
        })
      } else {
        util.showToast({
          icon: 'none',
          title: data.msg
        })
      }
      return true
    } catch (error) {
      return false
    }
  },
  async handleLogin() {
    util.showLoading({
      title: 'loading',
      mask: true
    })
    const data = await this.getMyDoctor(1)
    if (data) {
      this.setData(
        {
          isLogin: true
        },
        () => {
          this.getartList()
        }
      )
    } else {
      util.showToast({
        title: data.msg
      })
    }
    util.hideLoading()
  },
  handleSwatchNav(e) {
    const { index } = e.currentTarget.dataset
    const { artCurrent, isLogin } = this.data
    this.setData(
      {
        currentNav: index
      },
      () => {
        if (index === 1 && isLogin) {
          this.scrollTabs = this.selectComponent('#scrollTabs')
          console.log(artCurrent, index, 229)
          this.scrollTabs.setData(
            {
              currentTab: artCurrent
            },
            () => {
              if (artCurrent === 0) {
                this.getartList()
              }
            }
          )
        }
        if (index === 0 && isLogin) {
          this.getMyDoctor(1)
        }
      }
    )
  },
  // 监听滚动黏贴定位
  onNavScroll(e) {
    const { isFixed } = e.detail
    this.setData({
      isFixed
    })
  },
  // 切换资讯tag
  onSwatchTab(params) {
    // 切换我的医生-医学资讯 保留tab选中状态
    this.setData(
      {
        artHasNext: false,
        'artListQuery.page': 1,
        'artListQuery.groupId': params.detail.id,
        artCurrent: params.detail.index
      },
      () => {
        this.getartList()
      }
    )
  },
  //获取资讯内容
  async getartList() {
    util.showLoading({
      title: 'loading',
      mask: true
    })
    try {
      const { artList, artListQuery } = this.data
      const { data } = await util.request(api.articleList, {
        ...artListQuery
      }, 'get', 1, false)
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      console.log(artListQuery.page, 283)
      this.setData({
        artList:
          artListQuery.page > 1
            ? this.data.artList.concat(data.data.result)
            : data.data.result,
        artHasNext: data.data.hasNext
      })
      console.log(artList.concat(data.data.result), 291)
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  getDdepartments() {
    util.request(api.citydepart, {}, 'get', 1, false)
      .then(res => {
        if (res.data.code === 0) {
          this.data.items = res.data.data.departments.list.map(item => {
            return {
              text: item.name,
              id: item.id,
              icon: item.icon,
              default: item.icon ? item.icon : api.ImgUrl + `images/department/subject_def_${item.code}.png`
            }
          })
        }
        this.setData({
          departments: this.data.items.slice(0, 4)
        })
      })
  },
  // 图片加载失败
  onImageError(event) {
    console.log('图片加载失败：', event.detail.errMsg)
    this.setData({
      departments: this.data.departments.map(item => {
        item.default = api.ImgUrl + `images/department/<EMAIL>`
        return item
      })
    })
  },
  async getList() {
    try {
      const { data } = await util.request(api.noticeList, {}, 'get', 1, false)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        noticeList: data.data.result
      })
      console.log(data.data.result[0], 333)
    } catch (error) {
      throw new Error(error)
    }
  },
  async getBannerList() {
    try {
      const { data } = await util.request(api.bannerList, {}, 'get', 1, false)
      let bannerList = []

      // 添加默认AI banner
      bannerList.push(this.data.defaultBanner)

      // 如果接口返回数据,合并数据
      if (data.code === 0 && data.data && data.data.length > 0) {
        bannerList = bannerList.concat(data.data)
      }

      this.setData({
        bannerList: bannerList
      })
    } catch (error) {
      // 发生错误时至少显示默认banner
      this.setData({
        bannerList: [this.data.defaultBanner]
      })
      throw new Error(error)
    }
  },
  handleDetail(e) {
    const { type, id, url } = e.currentTarget.dataset
    const token = wx.getStorageSync('token')

    // 检查是否登录
    if (!token) {
      wx.navigateTo({
        url: '/pages/auth/login/login'
      })
      return
    }

    // 如果是AI类型且已登录但没有手机号,通过点击按钮触发获取手机号
    if (type === 'AI' && !this.data.hasPhone) {
      // 存储当前banner数据,用于获取手机号后的跳转
      this.setData({
        pendingBannerData: { type, id, url }
      })
      return
    }

    // 其他情况直接处理跳转
    this.handleBannerNavigation({ type, id, url })
  },

  // 获取手机号成功回调
  async getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      try {
        // 调用获取手机号接口
        const res = await util.request(util.getRealUrl(api.AuthGetPhone, e.detail.code), {}, 'get')
        if (res.data.code === 0) {
          // 更新本地存储的用户信息
          const userInfo = wx.getStorageSync('userInfo') || {}
          userInfo.phone = res.data.data.phoneNumber
          util.setUserInfo(userInfo)
          // 上传手机号
          const uploadRes = await util.request(api.uploadUserInfo, { phone: res.data.data.phoneNumber }, 'post', 1, false)
          if (uploadRes.data.code !== 0) {
            util.showToast({ title: '手机号上传失败', icon: 'none' })
            return
          }

          // 更新页面状态并执行跳转
          this.setData({
            hasPhone: true
          }, () => {
            // 在状态更新完成后执行跳转
            if (this.data.pendingBannerData) {
              const { type } = this.data.pendingBannerData
              if (type === 'AI') {
                // AI类型直接调用AI跳转逻辑
                this.handleAINavigation()
              } else if (type === 'VIDEO_CONSULT') {
                // 全科视频问诊类型调用H5跳转逻辑
                this.handleVideoConsultNavigation()
              } else {
                // 其他类型使用原有的banner跳转逻辑
                this.handleBannerNavigation(this.data.pendingBannerData)
              }
              this.setData({ pendingBannerData: null })
            }
          })
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      } catch (error) {
        util.showToast({
          title: '获取手机号失败',
          icon: 'none'
        })
      }
    }
  },

  // 处理banner跳转逻辑
  async handleBannerNavigation(bannerData) {
    const { type, id, url } = bannerData

    // 处理AI banner的特殊跳转
    if (type === 'AI') {
      // 获取带有缓存策略的token
      const tokenData = await this.getAIToken()
      if (!tokenData) {
        return
      }

      const { token } = tokenData
      let baseUrl = ''
      let projectCode = ''
      // 根据小程序版本设置不同的baseUrl
      const { envVersion } = wx.getAccountInfoSync().miniProgram

      if (envVersion === 'develop') {
        projectCode = 'CONSULT24123015231400163'
        baseUrl = 'https://ewphlp-uat.cignacmbhealth.com' // SIT环境
      } else if (envVersion === 'trial') {
        projectCode = 'CONSULT24123015231400163'
        baseUrl = 'https://ewphlp-uat.cignacmbhealth.com' // UAT环境
      } else {
        projectCode = 'CONSULT25031116305100191'
        baseUrl = 'https://cignacmbhealth.com' // 生产环境
      }
      // 获取完整的AI跳转链接
      const aiUrl = `${baseUrl}/mb-web/medical-guide/#/index/${projectCode}?token=${token}`
      wx.navigateTo({
        url: `/pages/webView/index?url=${encodeURIComponent(aiUrl)}`
      })
      return
    }

    // 其他banner的原有跳转逻辑
    if (type === 1) {
      wx.navigateTo({
        url: `/pages/article/articleDetail/index?id=${id}&type=banner`
      })
      // console.log('图文')
    } else {
      wx.navigateTo({
        url: `/pages/webView/index?url=${encodeURIComponent(url)}`
      })
      // console.log('外部链接')
    }
  },

  /**
   * 获取AI Token，带缓存策略
   * 根据expiresIn判断token是否过期，过期则重新请求
   */
  async getAIToken() {
    try {
      // 从缓存中获取token信息
      const cachedTokenInfo = wx.getStorageSync('ai-token-info')
      const now = Date.now()

      // 如果缓存中有token且未过期，直接返回
      if (cachedTokenInfo && cachedTokenInfo.expiresAt > now) {
        console.log('使用缓存的AI token')
        return {
          token: cachedTokenInfo.token,
          expiresIn: Math.floor((cachedTokenInfo.expiresAt - now) / 1000) // 剩余有效期（秒）
        }
      }

      // 缓存不存在或已过期，重新请求token
      console.log('重新请求AI token')
      const response = await util.request(api.changeToken, {}, 'get')

      if (response.data.code !== 0) {
        util.showToast({
          title: response.data.msg,
          icon: 'none',
          duration: 3000
        })
        return null
      }

      const { expiresIn, token } = response.data.data

      // 计算过期时间（毫秒），提前5分钟过期以确保安全
      const expiresAt = now + (expiresIn * 1000) - (5 * 60 * 1000)

      // 保存到本地存储
      wx.setStorageSync('xn-token', token)
      wx.setStorageSync('ai-token-info', {
        token,
        expiresAt,
        createdAt: now
      })

      return { token, expiresIn }
    } catch (error) {
      console.error('获取AI Token失败:', error)
      util.showToast({
        title: '获取AI服务失败，请稍后再试',
        icon: 'none',
        duration: 3000
      })
      return null
    }
  },

  /**
   * 获取H5 Token，带缓存策略
   * 根据expiresIn判断token是否过期，过期则重新请求
   */
  async getH5Token() {
    try {
      // 从缓存中获取token信息
      const cachedTokenInfo = wx.getStorageSync('h5-token-info')
      const now = Date.now()

      // 如果缓存中有token且未过期，直接返回
      if (cachedTokenInfo && cachedTokenInfo.expiresAt > now) {
        console.log('使用缓存的H5 token')
        return {
          token: cachedTokenInfo.token,
          expiresIn: Math.floor((cachedTokenInfo.expiresAt - now) / 1000) // 剩余有效期（秒）
        }
      }

      // 缓存不存在或已过期，重新请求token
      console.log('重新请求H5 token')
      const response = await util.request(api.getH5Token, {}, 'get')

      if (response.data.code !== 0) {
        util.showToast({
          title: response.data.msg,
          icon: 'none',
          duration: 3000
        })
        return null
      }

      const { expiresIn, token } = response.data.data

      // 计算过期时间（毫秒），提前5分钟过期以确保安全
      const expiresAt = now + (expiresIn * 1000) - (5 * 60 * 1000)

      // 保存到本地存储
      wx.setStorageSync('h5-token-info', {
        token,
        expiresAt,
        createdAt: now
      })

      return { token, expiresIn }
    } catch (error) {
      console.error('获取H5 Token失败:', error)
      util.showToast({
        title: '获取视频问诊服务失败，请稍后再试',
        icon: 'none',
        duration: 3000
      })
      return null
    }
  },

  /**
   * 获取常见疾病列表
   */
  async fetchDiseaseList() {
    try {
      const { data } = await util.request(api.diseaseList, { limit: 20 }, 'get', 1, false)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        commonDiseaseList: data.data
      })
    } catch (error) {
      throw new Error(error)
    }
  }
})
