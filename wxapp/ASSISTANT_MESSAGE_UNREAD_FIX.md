# 小助理消息未读计算修复

## 问题描述

小助理消息（type=10015 和 type=10016）虽然 `consultType` 也是 2（视频问诊），但这类消息不应该计算到未读消息数量中。

### 问题现象

1. **小助理消息特征**：
   - `consultType`: 2（视频问诊）
   - `type`: 10015（小助理问候语）或 10016（您发起了视频问诊）
   - `consultId`: 'default'

2. **问题**：
   - 小助理消息被计算到未读消息数量中
   - 导致用户看到不必要的未读消息提示

## 修复方案

### 1. 添加小助理消息识别方法

在 `app.js` 中添加 `isAssistantMessage` 方法：

```javascript
// 判断是否为小助理消息
isAssistantMessage(message) {
  // 小助理消息的特征：
  // 1. type 为 10015（小助理问候语）或 10016（您发起了视频问诊）
  // 2. consultId 为 'default'
  // 3. 通常 consultType 为 2（视频问诊）
  const assistantMessageTypes = [10015, 10016]
  return assistantMessageTypes.includes(message.type) && message.consultId === 'default'
}
```

### 2. 修改未读消息计算逻辑

在 `onMessageArrived` 方法中，修改未读消息计算逻辑：

#### 修改前：
```javascript
// 检查是否为视频问诊消息，如果是则不计算未读消息
if (isVideoConsult) {
  console.log('收到视频问诊消息(consultType=2)，跳过未读消息计算:', message)
} else {
  // 只有非视频问诊消息才计算未读消息
  this.getMessageNum()
}
```

#### 修改后：
```javascript
// 检查是否为视频问诊消息或小助理消息，如果是则不计算未读消息
if (isVideoConsult) {
  console.log('收到视频问诊消息(consultType=2)，跳过未读消息计算:', message)
} else if (this.isAssistantMessage(message)) {
  console.log('收到小助理消息，跳过未读消息计算:', message)
} else {
  // 只有非视频问诊消息且非小助理消息才计算未读消息
  this.getMessageNum()
}
```

## 识别逻辑

### 小助理消息识别条件

1. **消息类型匹配**：
   - `message.type === 10015`（小助理问候语）
   - `message.type === 10016`（您发起了视频问诊）

2. **consultId 匹配**：
   - `message.consultId === 'default'`

### 消息处理流程

```
消息到达
↓
判断 consultType === 2？
├─ 是 → 跳过未读消息计算（视频问诊消息）
└─ 否 → 继续判断
    ↓
    判断是否为小助理消息？
    ├─ 是 → 跳过未读消息计算（小助理消息）
    └─ 否 → 计算未读消息（普通图文问诊消息）
```

## 消息类型说明

### 需要跳过未读计算的消息

1. **视频问诊消息**：
   - `consultType`: 2
   - 所有类型的视频问诊相关消息

2. **小助理消息**：
   - `type`: 10015（小助理问候语）
   - `type`: 10016（您发起了视频问诊）
   - `consultId`: 'default'

### 需要计算未读的消息

1. **图文问诊消息**：
   - `consultType`: 1 或 null 或 undefined
   - 普通的文本、图片、语音等消息

## 调试信息

### 修复后的日志输出

```javascript
// 视频问诊消息
"收到视频问诊消息(consultType=2)，跳过未读消息计算: {type: 1, consultType: 2, consultId: '123456', ...}"

// 小助理消息
"收到小助理消息，跳过未读消息计算: {type: 10015, consultType: 2, consultId: 'default', ...}"

// 普通图文问诊消息
"计算未读消息: {type: 1, consultType: 1, ...}"
```

## 测试场景

### 1. 小助理问候语消息

```javascript
const assistantGreeting = {
  type: 10015,
  consultType: 2,
  consultId: 'default',
  content: '我是您的招商信诺助理-小诺 健康问题请点击蓝色按钮【呼叫视频医生】',
  from: { id: 'assistant', name: '小诺助理' },
  to: { id: 'patient123' }
}

// 预期结果：不计算未读消息
```

### 2. 发起视频问诊消息

```javascript
const videoInitMessage = {
  type: 10016,
  consultType: 2,
  consultId: 'default',
  content: '您发起了视频问诊',
  from: { id: 'system' },
  to: { id: 'patient123' }
}

// 预期结果：不计算未读消息
```

### 3. 普通视频问诊消息

```javascript
const normalVideoMessage = {
  type: 1,
  consultType: 2,
  consultId: '123456',
  content: '医生的回复消息',
  from: { id: 'doctor123', name: '张医生' },
  to: { id: 'patient123' }
}

// 预期结果：不计算未读消息（因为是视频问诊）
```

### 4. 普通图文问诊消息

```javascript
const normalTextMessage = {
  type: 1,
  consultType: 1,
  content: '医生的回复消息',
  from: { id: 'doctor123', name: '张医生' },
  to: { id: 'patient123' }
}

// 预期结果：计算未读消息
```

## 边界情况处理

### 1. consultId 不是 'default' 的 type=10015 消息

```javascript
const message = {
  type: 10015,
  consultType: 2,
  consultId: '123456',  // 不是 'default'
  content: '某些内容'
}

// 结果：不被识别为小助理消息，但因为 consultType=2 仍然不计算未读
```

### 2. consultId 是 'default' 但 type 不是 10015/10016

```javascript
const message = {
  type: 1,
  consultType: 2,
  consultId: 'default',
  content: '某些内容'
}

// 结果：不被识别为小助理消息，但因为 consultType=2 仍然不计算未读
```

### 3. type 是 10015 但 consultType 不是 2

```javascript
const message = {
  type: 10015,
  consultType: 1,
  consultId: 'default',
  content: '某些内容'
}

// 结果：被识别为小助理消息，不计算未读
```

## 相关代码位置

- **文件**: `wxapp/app.js`
- **方法**: `onMessageArrived()`, `isAssistantMessage()`
- **行号**: 273-281 (未读消息计算逻辑), 64-70 (小助理消息识别方法)

## 业务影响

### 修复前

- ❌ 小助理消息被计算到未读消息中
- ❌ 用户看到不必要的未读消息提示
- ❌ 影响用户体验

### 修复后

- ✅ 小助理消息不计算未读消息
- ✅ 只有真正的用户消息才显示未读提示
- ✅ 提升用户体验

## 注意事项

### 1. 向后兼容性

- 修改保持了对现有消息处理逻辑的兼容性
- 只是增加了小助理消息的特殊处理

### 2. 扩展性

- 如果将来有其他类型的系统消息需要排除，可以在 `assistantMessageTypes` 数组中添加
- 识别条件可以根据需要进行调整

### 3. 调试友好

- 添加了详细的日志输出，便于问题排查
- 清晰的条件判断逻辑

## 总结

通过这次修复：

1. ✅ **准确识别**：正确识别小助理消息
2. ✅ **排除计算**：小助理消息不计算未读数量
3. ✅ **保持兼容**：不影响其他消息的处理逻辑
4. ✅ **调试友好**：提供详细的日志信息
5. ✅ **用户体验**：避免不必要的未读消息提示

现在小助理消息将不会被计算到未读消息数量中，用户只会看到真正需要关注的消息的未读提示。
